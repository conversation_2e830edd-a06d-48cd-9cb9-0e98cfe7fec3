#!/usr/bin/env python3
"""
测试PyTorch修复的脚本
验证DiTAS参考图像生成、Qua2SeDiMo运行和PyTorch FID评估是否正常工作
"""

import logging
import sys
import subprocess
import torch
from pathlib import Path
import numpy as np

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pytorch_fid_evaluator():
    """测试PyTorch FID评估器"""
    logger.info("Testing PyTorch FID evaluator...")
    
    try:
        # 创建测试数据
        test_dir = Path("test_fid_data")
        test_dir.mkdir(exist_ok=True)
        
        # 创建假的NPZ文件用于测试
        fake_images1 = np.random.randint(0, 256, (10, 256, 256, 3), dtype=np.uint8)
        fake_images2 = np.random.randint(0, 256, (10, 256, 256, 3), dtype=np.uint8)
        
        ref_npz = test_dir / "ref.npz"
        sample_npz = test_dir / "sample.npz"
        
        np.savez(ref_npz, arr_0=fake_images1)
        np.savez(sample_npz, arr_0=fake_images2)
        
        # 测试FID评估器
        result = subprocess.run([
            "python", "pytorch_fid_evaluator.py", 
            str(ref_npz), str(sample_npz),
            "--device", "cuda" if torch.cuda.is_available() else "cpu"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ PyTorch FID evaluator test passed")
            logger.info(f"Output: {result.stdout.strip()}")
            return True
        else:
            logger.error(f"✗ PyTorch FID evaluator test failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"✗ PyTorch FID evaluator test error: {e}")
        return False
    finally:
        # 清理测试文件
        import shutil
        if test_dir.exists():
            shutil.rmtree(test_dir, ignore_errors=True)

def test_ditas_reference_generation():
    """测试DiTAS参考图像生成修复"""
    logger.info("Testing DiTAS reference image generation fix...")
    
    try:
        # 检查DiTAS脚本是否存在
        ditas_script = Path("github-repos/DiTAS/sample_merge_TAS.py")
        if not ditas_script.exists():
            logger.error("✗ DiTAS script not found")
            return False
        
        # 检查修复是否应用
        with open(ditas_script, 'r') as f:
            content = f.read()
        
        if "if args.path is not None:" in content:
            logger.info("✓ DiTAS reference generation fix applied")
            return True
        else:
            logger.error("✗ DiTAS reference generation fix not applied")
            return False
            
    except Exception as e:
        logger.error(f"✗ DiTAS reference generation test error: {e}")
        return False

def test_qua2sedimo_multiquantizer_script():
    """测试Qua2SeDiMo multiquantizer脚本"""
    logger.info("Testing Qua2SeDiMo multiquantizer script...")
    
    try:
        # 检查脚本是否存在
        script_path = Path("create_qua2sedimo_multiquantizer.py")
        if not script_path.exists():
            logger.error("✗ Qua2SeDiMo multiquantizer script not found")
            return False
        
        # 测试脚本语法
        result = subprocess.run([
            "python", "-m", "py_compile", str(script_path)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ Qua2SeDiMo multiquantizer script syntax check passed")
            return True
        else:
            logger.error(f"✗ Qua2SeDiMo multiquantizer script syntax error: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"✗ Qua2SeDiMo multiquantizer script test error: {e}")
        return False

def test_run_benchmark_fixes():
    """测试run_benchmark.py中的修复"""
    logger.info("Testing run_benchmark.py fixes...")
    
    try:
        # 检查run_benchmark.py是否存在
        benchmark_script = Path("run_benchmark.py")
        if not benchmark_script.exists():
            logger.error("✗ run_benchmark.py not found")
            return False
        
        # 检查关键修复是否应用
        with open(benchmark_script, 'r') as f:
            content = f.read()
        
        fixes_applied = 0
        total_fixes = 4
        
        # 检查PyTorch FID评估器使用
        if "pytorch_fid_evaluator.py" in content:
            logger.info("✓ PyTorch FID evaluator integration applied")
            fixes_applied += 1
        else:
            logger.error("✗ PyTorch FID evaluator integration not applied")
        
        # 检查Qua2SeDiMo命令修复
        if "run_script([str(BASE_DIR / 'create_qua2sedimo_multiquantizer.py')]" in content:
            logger.info("✓ Qua2SeDiMo command fix applied")
            fixes_applied += 1
        else:
            logger.error("✗ Qua2SeDiMo command fix not applied")
        
        # 检查DiTAS NPZ重命名修复
        if "npz_files = list(out_dir.rglob('*.npz')) + list(out_dir.parent.glob(f'{out_dir.name}*.npz'))" in content:
            logger.info("✓ DiTAS NPZ renaming fix applied")
            fixes_applied += 1
        else:
            logger.error("✗ DiTAS NPZ renaming fix not applied")
        
        # 检查Qua2SeDiMo推理脚本调用修复
        if "run_script([str(custom_infer_script)] + [" in content:
            logger.info("✓ Qua2SeDiMo inference script call fix applied")
            fixes_applied += 1
        else:
            logger.error("✗ Qua2SeDiMo inference script call fix not applied")
        
        if fixes_applied == total_fixes:
            logger.info(f"✓ All {total_fixes} run_benchmark.py fixes applied")
            return True
        else:
            logger.error(f"✗ Only {fixes_applied}/{total_fixes} run_benchmark.py fixes applied")
            return False
            
    except Exception as e:
        logger.error(f"✗ run_benchmark.py fixes test error: {e}")
        return False

def test_environment_dependencies():
    """测试环境依赖"""
    logger.info("Testing environment dependencies...")
    
    try:
        # 测试PyTorch
        import torch
        logger.info(f"✓ PyTorch {torch.__version__} available")
        
        # 测试CUDA
        if torch.cuda.is_available():
            logger.info(f"✓ CUDA available with {torch.cuda.device_count()} devices")
        else:
            logger.warning("⚠ CUDA not available, will use CPU")
        
        # 测试torchvision
        import torchvision
        logger.info(f"✓ torchvision {torchvision.__version__} available")
        
        # 测试PIL
        from PIL import Image
        logger.info("✓ PIL available")
        
        # 测试numpy
        import numpy as np
        logger.info(f"✓ numpy {np.__version__} available")
        
        # 测试scipy
        from scipy import linalg
        logger.info("✓ scipy available")
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ Missing dependency: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ Environment test error: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("Starting PyTorch fixes test...")
    
    tests = [
        ("Environment Dependencies", test_environment_dependencies),
        ("PyTorch FID Evaluator", test_pytorch_fid_evaluator),
        ("DiTAS Reference Generation Fix", test_ditas_reference_generation),
        ("Qua2SeDiMo Multiquantizer Script", test_qua2sedimo_multiquantizer_script),
        ("run_benchmark.py Fixes", test_run_benchmark_fixes),
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        results[test_name] = test_func()
    
    # 总结结果
    logger.info("\n" + "="*60)
    logger.info("PYTORCH FIXES TEST SUMMARY")
    logger.info("="*60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        logger.info(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        logger.info("\n✓ All PyTorch fixes tests passed!")
        logger.info("The system should now work without TensorFlow dependencies.")
    else:
        logger.error("\n✗ Some PyTorch fixes tests failed.")
        logger.error("Please check the issues above before running the benchmark.")

if __name__ == "__main__":
    main()
