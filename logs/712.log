2025-08-01 10:48:46,761 - [Main] - [INFO] - --- Phase 1: Preparing Environment ---
2025-08-01 10:48:46,762 - [Main] - [INFO] - Preparation tasks will use GPU: 0
2025-08-01 10:48:46,762 - [Main] - [INFO] - Using pretrained DiT model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt
2025-08-01 10:48:46,762 - [Main] - [INFO] - Using local VAE model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model
2025-08-01 10:48:46,762 - [Main] - [INFO] - Reference images already exist (24 images), skipping generation.
2025-08-01 10:48:46,762 - [Main] - [INFO] - Running in Multi-GPU mode on GPUs: [0, 1, 2, 7]
2025-08-01 10:48:46,762 - [Main] - [INFO] - Tasks to run: ['ditas', 'ptq4dit', 'q_dit', 'qua2sedimo']
2025-08-01 10:48:46,786 - [qua2sedimo-GPU7] - [INFO] - Starting task on GPU 7
2025-08-01 10:48:46,786 - [ditas-GPU0] - [INFO] - Starting task on GPU 0
2025-08-01 10:48:46,786 - [qua2sedimo-GPU7] - [INFO] - Generating Qua2SeDiMo multiquantizer checkpoint...
2025-08-01 10:48:46,786 - [q_dit-GPU2] - [INFO] - Starting task on GPU 2
2025-08-01 10:48:46,786 - [qua2sedimo-GPU7] - [WARNING] - This may take a long time and require significant VRAM (24-32GB)
2025-08-01 10:48:46,787 - [qua2sedimo-GPU7] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant':
$ python -u /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/create_qua2sedimo_multiquantizer.py --weight_bit 4 --seed 1234 --dit_ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt --image_size 256 --output_dir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers

2025-08-01 10:48:46,787 - [ditas-GPU0] - [INFO] - DiTAS quantized model already exists at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8/W4A8_50_256.pt, skipping quantization step.
2025-08-01 10:48:46,786 - [ptq4dit-GPU1] - [INFO] - Starting task on GPU 1
2025-08-01 10:48:46,787 - [q_dit-GPU2] - [INFO] - Q-DiT FID samples already exist at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qdit_w4a8/samples.npz, skipping FID sampling step.
2025-08-01 10:48:46,787 - [ditas-GPU0] - [INFO] - Sampling for FID...
2025-08-01 10:48:46,787 - [ditas-GPU0] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS':
$ torchrun --nnodes=1 --nproc_per_node=1 /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/sample_merge_TAS.py --model DiT-XL/2 --image-size 256 --path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8/W4A8_50_256.pt --num-fid-samples 1000 --sample-dir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8 --global-seed 1234 --cfg-scale 1.5 --vae-path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model

2025-08-01 10:48:46,788 - [q_dit-GPU2] - [INFO] - Q-DiT visualization images already exist in /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qdit_w4a8/visuals, skipping visualization step.
2025-08-01 10:48:46,788 - [ptq4dit-GPU1] - [INFO] - PTQ4DiT quantized model already exists at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/qnn_model.pth, skipping quantization step.
2025-08-01 10:48:46,788 - [ptq4dit-GPU1] - [INFO] - Sampling for FID with PTQ4DiT...
2025-08-01 10:48:46,788 - [ptq4dit-GPU1] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT':
$ python -u /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/quant_sample.py --model DiT-XL/2 --image-size 256 --ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt --num-sampling-steps 50 --weight_bit 4 --act_bit 8 --sm_abit 16 --recon --outdir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8 --inference --n_c 1 --c_begin 0 --c_end 999 --resume --cali_ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/qnn_model.pth --cfg-scale 1.5 --seed 1234 --vae-path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model

2025-08-01 10:48:51,988 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 10:48:51 - INFO - __main__ -   Arguments: Namespace(model='DiT-XL/2', vae='mse', image_size=256, num_classes=1000, cfg_scale=1.5, num_sampling_steps=50, seed=1234, ckpt='/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt', outdir='/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8', ptq=False, weight_bit=4, act_bit=8, cali_ckpt='/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/qnn_model.pth', cali_data_path='sd_coco_sample1024_allst.pt', resume=True, cali_st=1, cali_n=1024, cali_iters=20000, cali_batch_size=32, cali_lr=0.0004, cali_p=2.4, sm_abit=16, recon=True, opt_mode='mse', vae_path='/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model', visualize=False, noise_path=None, labels_path=None, inference=True, n_c=1, c_begin=0, c_end=999)
2025-08-01 10:48:51,988 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 10:48:51 - INFO - __main__ -   Saving to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/2025-08-01-10-48-51
2025-08-01 10:48:52,142 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 10:48:52 - INFO - __main__ -   Loading full quantized model from: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/qnn_model.pth
2025-08-01 10:48:52,692 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Global seed set to 1234
2025-08-01 10:48:52,693 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-01 10:48:52,693 - INFO - Loading DiT model from /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt
2025-08-01 10:48:54,155 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Starting rank=0, seed=1234, world_size=1.
2025-08-01 10:48:57,196 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] An error occurred while trying to fetch /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model: Error no file named diffusion_pytorch_model.safetensors found in directory /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model.
2025-08-01 10:48:57,196 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] Defaulting to unsafe serialization. Pass `allow_pickle=False` to raise an error instead.
2025-08-01 10:48:57,373 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 10:48:57 - INFO - __main__ -   Applying intelligent quantization optimization for better image quality...
2025-08-01 10:48:57,388 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 10:48:57 - INFO - __main__ -   Optimized quantization for 28 attention layers
2025-08-01 10:48:57,388 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 10:48:57 - INFO - __main__ -   Performing quantization quality validation...
2025-08-01 10:48:57,835 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 10:48:57 - INFO - __main__ -   Quantization validation - MSE Error: 0.001896, Max Error: 0.166196
2025-08-01 10:48:57,836 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 10:48:57 - INFO - __main__ -   Quantization quality validation passed!
2025-08-01 10:48:59,263 - [ptq4dit-GPU1] - [INFO] - Converting PNGs from /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/inference to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/samples.npz
2025-08-01 10:48:59,263 - [ptq4dit-GPU1] - [WARNING] - No PNG files found in /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/inference.
2025-08-01 10:48:59,264 - [ptq4dit-GPU1] - [INFO] - PTQ4DiT visualization images already exist in /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/visuals, skipping visualization step.
2025-08-01 10:49:04,539 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Loading quantized model from /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8/W4A8_50_256.pt
2025-08-01 10:49:07,708 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-01 10:49:07,707 - INFO - Setting up quantization...
2025-08-01 10:49:07,708 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] 2025-08-01 10:49:07,708 - ERROR - Failed to create multiquantizer checkpoint: module diffusers.models has no attribute resnet
2025-08-01 10:49:07,708 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-01 10:49:07,708 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/create_qua2sedimo_multiquantizer.py", line 129, in <module>
2025-08-01 10:49:07,709 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] main()
2025-08-01 10:49:07,709 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/create_qua2sedimo_multiquantizer.py", line 126, in main
2025-08-01 10:49:07,709 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] raise e
2025-08-01 10:49:07,709 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/create_qua2sedimo_multiquantizer.py", line 75, in main
2025-08-01 10:49:07,709 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] qnn = QuantModelMultiQ(
2025-08-01 10:49:07,709 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/qdiff/quant_model.py", line 154, in __init__
2025-08-01 10:49:07,709 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] self.specials = get_specials(act_quant_params['leaf_param'], sdv15=sdv15)
2025-08-01 10:49:07,709 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/qdiff/quant_block.py", line 403, in get_specials
2025-08-01 10:49:07,709 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] specials[diffusers.models.resnet.ResnetBlock2D] = QuantDiffRB
2025-08-01 10:49:07,710 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/diffusers/utils/import_utils.py", line 876, in __getattr__
2025-08-01 10:49:07,710 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] raise AttributeError(f"module {self.__name__} has no attribute {name}")
2025-08-01 10:49:07,710 - [qua2sedimo-GPU7] - [INFO] - [SUBPROCESS] AttributeError: module diffusers.models has no attribute resnet
2025-08-01 10:49:08,909 - [qua2sedimo-GPU7] - [ERROR] - Benchmark for qua2sedimo failed.
Traceback (most recent call last):
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 273, in worker_task
    return method_name, func(env_vars)
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 545, in benchmark_qua2sedimo
    run_script([str(BASE_DIR / 'create_qua2sedimo_multiquantizer.py')] + [
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 127, in run_script
    raise subprocess.CalledProcessError(return_code, final_command, "\n".join(output_lines))
subprocess.CalledProcessError: Command '['python', '-u', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/create_qua2sedimo_multiquantizer.py', '--weight_bit', '4', '--seed', '1234', '--dit_ckpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt', '--image_size', '256', '--output_dir', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers']' returned non-zero exit status 1.
2025-08-01 10:49:15,551 - [ditas-GPU0] - [INFO] - [SUBPROCESS] An error occurred while trying to fetch /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model: Error no file named diffusion_pytorch_model.safetensors found in directory /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model.
2025-08-01 10:49:15,551 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Defaulting to unsafe serialization. Pass `allow_pickle=False` to raise an error instead.
2025-08-01 10:49:15,703 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Saving FID samples at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8
2025-08-01 10:49:15,704 - [ditas-GPU0] - [INFO] - [SUBPROCESS] /home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
2025-08-01 10:49:15,704 - [ditas-GPU0] - [INFO] - [SUBPROCESS] warnings.warn(  # warn only once
2025-08-01 10:49:15,704 - [ditas-GPU0] - [INFO] - [SUBPROCESS] [rank0]:[W801 10:49:15.756046462 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
2025-08-01 10:49:15,883 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Total number of images that will be sampled: 1024
2025-08-01 10:53:28,656 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 0%|          | 0/16 [00:00<?, ?it/s]