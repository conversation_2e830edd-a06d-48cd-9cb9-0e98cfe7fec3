2025-08-01 09:17:49,879 - [Main] - [INFO] - --- Phase 1: Preparing Environment ---
2025-08-01 09:17:49,879 - [Main] - [INFO] - Preparation tasks will use GPU: 0
2025-08-01 09:17:49,879 - [Main] - [INFO] - Using pretrained DiT model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt
2025-08-01 09:17:49,880 - [Main] - [INFO] - Using local VAE model: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model
2025-08-01 09:17:49,880 - [Main] - [INFO] - Generating reference images from unquantized DiT model...
2025-08-01 09:17:49,880 - [Main] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS':
$ torchrun --nnodes=1 --nproc_per_node=1 /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/sample_merge_TAS.py --model DiT-XL/2 --image-size 256 --ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt --sample-dir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/data/temp_reference --global-seed 1234 --cfg-scale 1.5 --vae-path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model --visualize --noise-path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/data/initial_noise/noise_z_256_24.pt --labels-path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/data/initial_noise/labels_y_24.pt

2025-08-01 09:17:57,147 - [Main] - [INFO] - [SUBPROCESS] Running in visualization mode on a single device.
2025-08-01 09:18:07,307 - [Main] - [INFO] - [SUBPROCESS] Loading quantized model from None
2025-08-01 09:18:07,311 - [Main] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-01 09:18:07,311 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/serialization.py", line 869, in _check_seekable
2025-08-01 09:18:07,311 - [Main] - [INFO] - [SUBPROCESS] f.seek(f.tell())
2025-08-01 09:18:07,311 - [Main] - [INFO] - [SUBPROCESS] AttributeError: 'NoneType' object has no attribute 'seek'
2025-08-01 09:18:07,312 - [Main] - [INFO] - [SUBPROCESS] During handling of the above exception, another exception occurred:
2025-08-01 09:18:07,312 - [Main] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-01 09:18:07,312 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/sample_merge_TAS.py", line 264, in <module>
2025-08-01 09:18:07,312 - [Main] - [INFO] - [SUBPROCESS] main(args)
2025-08-01 09:18:07,312 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/sample_merge_TAS.py", line 117, in main
2025-08-01 09:18:07,312 - [Main] - [INFO] - [SUBPROCESS] state_dict = torch.load(args.path, map_location="cpu")["model"]
2025-08-01 09:18:07,312 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/serialization.py", line 1479, in load
2025-08-01 09:18:07,312 - [Main] - [INFO] - [SUBPROCESS] with _open_file_like(f, "rb") as opened_file:
2025-08-01 09:18:07,312 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/serialization.py", line 764, in _open_file_like
2025-08-01 09:18:07,313 - [Main] - [INFO] - [SUBPROCESS] return _open_buffer_reader(name_or_buffer)
2025-08-01 09:18:07,313 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/serialization.py", line 749, in __init__
2025-08-01 09:18:07,313 - [Main] - [INFO] - [SUBPROCESS] _check_seekable(buffer)
2025-08-01 09:18:07,313 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/serialization.py", line 872, in _check_seekable
2025-08-01 09:18:07,313 - [Main] - [INFO] - [SUBPROCESS] raise_err_msg(["seek", "tell"], e)
2025-08-01 09:18:07,313 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/serialization.py", line 865, in raise_err_msg
2025-08-01 09:18:07,314 - [Main] - [INFO] - [SUBPROCESS] raise type(e)(msg)
2025-08-01 09:18:07,314 - [Main] - [INFO] - [SUBPROCESS] AttributeError: 'NoneType' object has no attribute 'seek'. You can only torch.load from a file that is seekable. Please pre-load the data into a buffer like io.BytesIO and try to load from it instead.
2025-08-01 09:18:08,606 - [Main] - [INFO] - [SUBPROCESS] E0801 09:18:08.605000 307221 site-packages/torch/distributed/elastic/multiprocessing/api.py:874] failed (exitcode: 1) local_rank: 0 (pid: 307323) of binary: /home/<USER>/.conda/envs/dquant-rag2/bin/python3.10
2025-08-01 09:18:08,610 - [Main] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-01 09:18:08,610 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/bin/torchrun", line 8, in <module>
2025-08-01 09:18:08,610 - [Main] - [INFO] - [SUBPROCESS] sys.exit(main())
2025-08-01 09:18:08,610 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/errors/__init__.py", line 355, in wrapper
2025-08-01 09:18:08,611 - [Main] - [INFO] - [SUBPROCESS] return f(*args, **kwargs)
2025-08-01 09:18:08,611 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/distributed/run.py", line 892, in main
2025-08-01 09:18:08,612 - [Main] - [INFO] - [SUBPROCESS] run(args)
2025-08-01 09:18:08,612 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/distributed/run.py", line 883, in run
2025-08-01 09:18:08,613 - [Main] - [INFO] - [SUBPROCESS] elastic_launch(
2025-08-01 09:18:08,613 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 139, in __call__
2025-08-01 09:18:08,613 - [Main] - [INFO] - [SUBPROCESS] return launch_agent(self._config, self._entrypoint, list(args))
2025-08-01 09:18:08,613 - [Main] - [INFO] - [SUBPROCESS] File "/home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 270, in launch_agent
2025-08-01 09:18:08,613 - [Main] - [INFO] - [SUBPROCESS] raise ChildFailedError(
2025-08-01 09:18:08,613 - [Main] - [INFO] - [SUBPROCESS] torch.distributed.elastic.multiprocessing.errors.ChildFailedError:
2025-08-01 09:18:08,613 - [Main] - [INFO] - [SUBPROCESS] ============================================================
2025-08-01 09:18:08,614 - [Main] - [INFO] - [SUBPROCESS] /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/sample_merge_TAS.py FAILED
2025-08-01 09:18:08,614 - [Main] - [INFO] - [SUBPROCESS] ------------------------------------------------------------
2025-08-01 09:18:08,614 - [Main] - [INFO] - [SUBPROCESS] Failures:
2025-08-01 09:18:08,614 - [Main] - [INFO] - [SUBPROCESS] <NO_OTHER_FAILURES>
2025-08-01 09:18:08,614 - [Main] - [INFO] - [SUBPROCESS] ------------------------------------------------------------
2025-08-01 09:18:08,614 - [Main] - [INFO] - [SUBPROCESS] Root Cause (first observed failure):
2025-08-01 09:18:08,614 - [Main] - [INFO] - [SUBPROCESS] [0]:
2025-08-01 09:18:08,614 - [Main] - [INFO] - [SUBPROCESS] time      : 2025-08-01_09:18:08
2025-08-01 09:18:08,614 - [Main] - [INFO] - [SUBPROCESS] host      : POC.Demonstration
2025-08-01 09:18:08,615 - [Main] - [INFO] - [SUBPROCESS] rank      : 0 (local_rank: 0)
2025-08-01 09:18:08,615 - [Main] - [INFO] - [SUBPROCESS] exitcode  : 1 (pid: 307323)
2025-08-01 09:18:08,615 - [Main] - [INFO] - [SUBPROCESS] error_file: <N/A>
2025-08-01 09:18:08,615 - [Main] - [INFO] - [SUBPROCESS] traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
2025-08-01 09:18:08,615 - [Main] - [INFO] - [SUBPROCESS] ============================================================
2025-08-01 09:18:09,168 - [Main] - [WARNING] - Failed to generate reference images using DiTAS: Command '['torchrun', '--nnodes=1', '--nproc_per_node=1', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/sample_merge_TAS.py', '--model', 'DiT-XL/2', '--image-size', '256', '--ckpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt', '--sample-dir', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/data/temp_reference', '--global-seed', '1234', '--cfg-scale', '1.5', '--vae-path', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model', '--visualize', '--noise-path', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/data/initial_noise/noise_z_256_24.pt', '--labels-path', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/data/initial_noise/labels_y_24.pt']' returned non-zero exit status 1.
2025-08-01 09:18:09,168 - [Main] - [INFO] - Creating placeholder reference images...
2025-08-01 09:18:09,292 - [Main] - [INFO] - Reference images ready in /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/data/reference_images
2025-08-01 09:18:09,292 - [Main] - [INFO] - Running in Multi-GPU mode on GPUs: [0, 1, 2]
2025-08-01 09:18:09,292 - [Main] - [INFO] - Tasks to run: ['ditas', 'ptq4dit', 'q_dit', 'qua2sedimo']
2025-08-01 09:18:09,316 - [ptq4dit-GPU1] - [INFO] - Starting task on GPU 1
2025-08-01 09:18:09,316 - [q_dit-GPU2] - [INFO] - Starting task on GPU 2
2025-08-01 09:18:09,316 - [ditas-GPU0] - [INFO] - Starting task on GPU 0
2025-08-01 09:18:09,317 - [ptq4dit-GPU1] - [INFO] - PTQ4DiT quantized model already exists at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/qnn_model.pth, skipping quantization step.
2025-08-01 09:18:09,317 - [q_dit-GPU2] - [INFO] - Q-DiT FID samples already exist at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qdit_w4a8/samples.npz, skipping FID sampling step.
2025-08-01 09:18:09,317 - [ptq4dit-GPU1] - [INFO] - Sampling for FID with PTQ4DiT...
2025-08-01 09:18:09,317 - [ditas-GPU0] - [INFO] - DiTAS quantized model already exists at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8/W4A8_50_256.pt, skipping quantization step.
2025-08-01 09:18:09,318 - [ditas-GPU0] - [INFO] - Sampling for FID...
2025-08-01 09:18:09,318 - [ptq4dit-GPU1] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT':
$ python -u /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/PTQ4DiT/quant_sample.py --model DiT-XL/2 --image-size 256 --ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt --num-sampling-steps 50 --weight_bit 4 --act_bit 8 --sm_abit 16 --recon --outdir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8 --inference --n_c 1 --c_begin 0 --c_end 999 --resume --cali_ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/qnn_model.pth --cfg-scale 1.5 --seed 1234 --vae-path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model

2025-08-01 09:18:09,318 - [q_dit-GPU2] - [INFO] - Q-DiT visualization images already exist in /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qdit_w4a8/visuals, skipping visualization step.
2025-08-01 09:18:09,318 - [ditas-GPU0] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS':
$ torchrun --nnodes=1 --nproc_per_node=1 /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/sample_merge_TAS.py --model DiT-XL/2 --image-size 256 --path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8/W4A8_50_256.pt --num-fid-samples 1000 --sample-dir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8 --global-seed 1234 --cfg-scale 1.5 --vae-path /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model

2025-08-01 09:18:09,320 - [qua2sedimo-GPU0] - [INFO] - Starting task on GPU 0
2025-08-01 09:18:09,320 - [qua2sedimo-GPU0] - [INFO] - Generating Qua2SeDiMo multiquantizer checkpoint...
2025-08-01 09:18:09,320 - [qua2sedimo-GPU0] - [WARNING] - This may take a long time and require significant VRAM (24-32GB)
2025-08-01 09:18:09,321 - [qua2sedimo-GPU0] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant':
$ python -u python /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/create_qua2sedimo_multiquantizer.py --weight_bit 4 --seed 1234 --dit_ckpt /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt --image_size 256 --output_dir /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers

2025-08-01 09:18:09,333 - [qua2sedimo-GPU0] - [INFO] - [SUBPROCESS] python: can't open file '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/python': [Errno 2] No such file or directory
2025-08-01 09:18:09,335 - [qua2sedimo-GPU0] - [ERROR] - Benchmark for qua2sedimo failed.
Traceback (most recent call last):
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 273, in worker_task
    return method_name, func(env_vars)
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 539, in benchmark_qua2sedimo
    run_script(['python', str(BASE_DIR / 'create_qua2sedimo_multiquantizer.py')] + [
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 127, in run_script
    raise subprocess.CalledProcessError(return_code, final_command, "\n".join(output_lines))
subprocess.CalledProcessError: Command '['python', '-u', 'python', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/create_qua2sedimo_multiquantizer.py', '--weight_bit', '4', '--seed', '1234', '--dit_ckpt', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt', '--image_size', '256', '--output_dir', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/Qua2SeDiMo/multiquantizers']' returned non-zero exit status 2.
2025-08-01 09:18:14,434 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 09:18:14 - INFO - __main__ -   Arguments: Namespace(model='DiT-XL/2', vae='mse', image_size=256, num_classes=1000, cfg_scale=1.5, num_sampling_steps=50, seed=1234, ckpt='/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/DiT-XL-2-256x256.pt', outdir='/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8', ptq=False, weight_bit=4, act_bit=8, cali_ckpt='/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/qnn_model.pth', cali_data_path='sd_coco_sample1024_allst.pt', resume=True, cali_st=1, cali_n=1024, cali_iters=20000, cali_batch_size=32, cali_lr=0.0004, cali_p=2.4, sm_abit=16, recon=True, opt_mode='mse', vae_path='/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model', visualize=False, noise_path=None, labels_path=None, inference=True, n_c=1, c_begin=0, c_end=999)
2025-08-01 09:18:14,434 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 09:18:14 - INFO - __main__ -   Saving to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/2025-08-01-09-18-14
2025-08-01 09:18:14,586 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 09:18:14 - INFO - __main__ -   Loading full quantized model from: /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/qnn_model.pth
2025-08-01 09:18:16,578 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Starting rank=0, seed=1234, world_size=1.
2025-08-01 09:18:19,922 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] An error occurred while trying to fetch /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model: Error no file named diffusion_pytorch_model.safetensors found in directory /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model.
2025-08-01 09:18:19,923 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] Defaulting to unsafe serialization. Pass `allow_pickle=False` to raise an error instead.
2025-08-01 09:18:20,119 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 09:18:20 - INFO - __main__ -   Applying intelligent quantization optimization for better image quality...
2025-08-01 09:18:20,131 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 09:18:20 - INFO - __main__ -   Optimized quantization for 28 attention layers
2025-08-01 09:18:20,131 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 09:18:20 - INFO - __main__ -   Performing quantization quality validation...
2025-08-01 09:18:20,571 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 09:18:20 - INFO - __main__ -   Quantization validation - MSE Error: 0.001896, Max Error: 0.166196
2025-08-01 09:18:20,571 - [ptq4dit-GPU1] - [INFO] - [SUBPROCESS] 08/01/2025 09:18:20 - INFO - __main__ -   Quantization quality validation passed!
2025-08-01 09:18:22,144 - [ptq4dit-GPU1] - [INFO] - Converting PNGs from /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/inference to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/samples.npz
2025-08-01 09:18:22,144 - [ptq4dit-GPU1] - [WARNING] - No PNG files found in /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/inference.
2025-08-01 09:18:22,145 - [ptq4dit-GPU1] - [INFO] - PTQ4DiT visualization images already exist in /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ptq4dit_w4a8/visuals, skipping visualization step.
2025-08-01 09:18:27,150 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Loading quantized model from /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8/W4A8_50_256.pt
2025-08-01 09:18:38,150 - [ditas-GPU0] - [INFO] - [SUBPROCESS] An error occurred while trying to fetch /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model: Error no file named diffusion_pytorch_model.safetensors found in directory /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/models/local_vae_model.
2025-08-01 09:18:38,150 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Defaulting to unsafe serialization. Pass `allow_pickle=False` to raise an error instead.
2025-08-01 09:18:38,320 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Saving FID samples at /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8
2025-08-01 09:18:38,320 - [ditas-GPU0] - [INFO] - [SUBPROCESS] /home/<USER>/.conda/envs/dquant-rag2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
2025-08-01 09:18:38,320 - [ditas-GPU0] - [INFO] - [SUBPROCESS] warnings.warn(  # warn only once
2025-08-01 09:18:38,321 - [ditas-GPU0] - [INFO] - [SUBPROCESS] [rank0]:[W801 09:18:38.372552699 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
2025-08-01 09:18:38,495 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Total number of images that will be sampled: 1024
2025-08-01 09:22:52,421 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 0%|          | 0/16 [00:00<?, ?it/s]
2025-08-01 09:27:05,045 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 6%|▋         | 1/16 [04:13<1:03:28, 253.92s/it]
2025-08-01 09:31:18,901 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 12%|█▎        | 2/16 [08:26<59:04, 253.16s/it]
2025-08-01 09:35:30,954 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 19%|█▉        | 3/16 [12:40<54:55, 253.48s/it]
2025-08-01 09:39:44,692 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 25%|██▌       | 4/16 [16:52<50:34, 252.92s/it]
2025-08-01 09:43:57,724 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 31%|███▏      | 5/16 [21:06<46:25, 253.21s/it]
2025-08-01 09:48:11,203 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 38%|███▊      | 6/16 [25:19<42:11, 253.15s/it]
2025-08-01 09:52:23,963 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 44%|████▍     | 7/16 [29:32<37:59, 253.26s/it]
2025-08-01 09:56:36,488 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 50%|█████     | 8/16 [33:45<33:44, 253.10s/it]
2025-08-01 10:00:48,110 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 56%|█████▋    | 9/16 [37:57<29:30, 252.92s/it]
2025-08-01 10:05:00,643 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 62%|██████▎   | 10/16 [42:09<25:15, 252.52s/it]
2025-08-01 10:09:12,101 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 69%|██████▉   | 11/16 [46:22<21:02, 252.52s/it]
2025-08-01 10:13:24,020 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 75%|███████▌  | 12/16 [50:33<16:48, 252.20s/it]
2025-08-01 10:17:35,389 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 81%|████████▏ | 13/16 [54:45<12:36, 252.11s/it]
2025-08-01 10:21:48,252 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 88%|████████▊ | 14/16 [58:56<08:23, 251.89s/it]
2025-08-01 10:26:00,646 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 94%|█████████▍| 15/16 [1:03:09<04:12, 252.18s/it]
2025-08-01 10:26:00,646 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 100%|██████████| 16/16 [1:07:22<00:00, 252.25s/it]
2025-08-01 10:26:00,647 - [ditas-GPU0] - [INFO] - [SUBPROCESS] 100%|██████████| 16/16 [1:07:22<00:00, 252.63s/it]
2025-08-01 10:26:00,747 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:   0%|          | 0/1000 [00:00<?, ?it/s]
2025-08-01 10:26:00,850 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:   3%|▎         | 31/1000 [00:00<00:03, 309.67it/s]
2025-08-01 10:26:00,952 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:   6%|▋         | 63/1000 [00:00<00:03, 311.80it/s]
2025-08-01 10:26:01,058 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  10%|▉         | 95/1000 [00:00<00:02, 312.59it/s]
2025-08-01 10:26:01,159 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  13%|█▎        | 127/1000 [00:00<00:02, 308.07it/s]
2025-08-01 10:26:01,260 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  16%|█▌        | 158/1000 [00:00<00:02, 306.96it/s]
2025-08-01 10:26:01,360 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  19%|█▉        | 191/1000 [00:00<00:02, 314.60it/s]
2025-08-01 10:26:01,462 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  22%|██▏       | 224/1000 [00:00<00:02, 319.43it/s]
2025-08-01 10:26:01,563 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  26%|██▌       | 257/1000 [00:00<00:02, 320.51it/s]
2025-08-01 10:26:01,665 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  29%|██▉       | 290/1000 [00:00<00:02, 321.91it/s]
2025-08-01 10:26:01,769 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  32%|███▏      | 323/1000 [00:01<00:02, 322.84it/s]
2025-08-01 10:26:01,870 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  36%|███▌      | 356/1000 [00:01<00:02, 321.63it/s]
2025-08-01 10:26:01,973 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  39%|███▉      | 389/1000 [00:01<00:01, 322.42it/s]
2025-08-01 10:26:02,077 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  42%|████▏     | 422/1000 [00:01<00:01, 322.40it/s]
2025-08-01 10:26:02,181 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  46%|████▌     | 455/1000 [00:01<00:01, 320.67it/s]
2025-08-01 10:26:02,282 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  49%|████▉     | 488/1000 [00:01<00:01, 319.79it/s]
2025-08-01 10:26:02,387 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  52%|█████▏    | 521/1000 [00:01<00:01, 321.29it/s]
2025-08-01 10:26:02,491 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  55%|█████▌    | 554/1000 [00:01<00:01, 319.54it/s]
2025-08-01 10:26:02,592 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  59%|█████▊    | 586/1000 [00:01<00:01, 315.75it/s]
2025-08-01 10:26:02,692 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  62%|██████▏   | 618/1000 [00:01<00:01, 316.48it/s]
2025-08-01 10:26:02,794 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  65%|██████▌   | 650/1000 [00:02<00:01, 317.26it/s]
2025-08-01 10:26:02,896 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  68%|██████▊   | 684/1000 [00:02<00:00, 321.72it/s]
2025-08-01 10:26:02,997 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  72%|███████▏  | 717/1000 [00:02<00:00, 322.50it/s]
2025-08-01 10:26:03,101 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  75%|███████▌  | 750/1000 [00:02<00:00, 323.37it/s]
2025-08-01 10:26:03,204 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  78%|███████▊  | 783/1000 [00:02<00:00, 322.22it/s]
2025-08-01 10:26:03,307 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  82%|████████▏ | 817/1000 [00:02<00:00, 324.05it/s]
2025-08-01 10:26:03,410 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  85%|████████▌ | 850/1000 [00:02<00:00, 323.37it/s]
2025-08-01 10:26:03,517 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  88%|████████▊ | 883/1000 [00:02<00:00, 322.05it/s]
2025-08-01 10:26:03,620 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  92%|█████████▏| 916/1000 [00:02<00:00, 317.87it/s]
2025-08-01 10:26:03,722 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  95%|█████████▍| 949/1000 [00:02<00:00, 319.27it/s]
2025-08-01 10:26:03,782 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples:  98%|█████████▊| 981/1000 [00:03<00:00, 317.35it/s]
2025-08-01 10:26:03,782 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Building .npz file from samples: 100%|██████████| 1000/1000 [00:03<00:00, 319.06it/s]
2025-08-01 10:26:04,817 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Saved .npz file to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8.npz [shape=(1000, 256, 256, 3)].
2025-08-01 10:26:04,818 - [ditas-GPU0] - [INFO] - [SUBPROCESS] Done.
2025-08-01 10:26:07,381 - [ditas-GPU0] - [INFO] - DiTAS visualization images already exist in /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/ditas_w4a8/visuals, skipping visualization step.
2025-08-01 10:26:07,399 - [Evaluator] - [INFO] - --- Phase 3: Evaluating FID and Stitching Images ---
2025-08-01 10:26:07,403 - [Evaluator] - [WARNING] - Sample NPZ not found for ditas. Skipping evaluation.
2025-08-01 10:26:07,403 - [Evaluator] - [INFO] - --- Evaluating q_dit ---
2025-08-01 10:26:07,403 - [Evaluator] - [INFO] - Executing in '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS':
$ python -u /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/evaluator.py /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/data/reference_batch/VIRTUAL_imagenet256_labeled.npz /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qdit_w4a8/samples.npz

2025-08-01 10:26:07,651 - [Evaluator] - [INFO] - [SUBPROCESS] Traceback (most recent call last):
2025-08-01 10:26:07,651 - [Evaluator] - [INFO] - [SUBPROCESS] File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/evaluator.py", line 16, in <module>
2025-08-01 10:26:07,651 - [Evaluator] - [INFO] - [SUBPROCESS] import tensorflow.compat.v1 as tf
2025-08-01 10:26:07,651 - [Evaluator] - [INFO] - [SUBPROCESS] ModuleNotFoundError: No module named 'tensorflow'
2025-08-01 10:26:07,677 - [Evaluator] - [ERROR] - Evaluation failed for q_dit
Traceback (most recent call last):
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 922, in evaluate_and_summarize
    output = run_script([str(evaluator_script), str(PATHS['reference_batch'] / REF_BATCH_NAME), str(sample_npz_path)],
  File "/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/run_benchmark.py", line 127, in run_script
    raise subprocess.CalledProcessError(return_code, final_command, "\n".join(output_lines))
subprocess.CalledProcessError: Command '['python', '-u', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/github-repos/DiTAS/evaluator.py', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/data/reference_batch/VIRTUAL_imagenet256_labeled.npz', '/home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/qdit_w4a8/samples.npz']' returned non-zero exit status 1.


================================================================================
                         Benchmark Summary
================================================================================
Method               | FID        | sFID       | Inception Score      | Precision  | Recall    
-----------------------------------------------------------------------------------------------
================================================================================
2025-08-01 10:26:07,678 - [Evaluator] - [INFO] - Creating multiple visual comparison groups with reference images...
2025-08-01 10:26:07,678 - [Evaluator] - [INFO] - Creating visual comparison group 1 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/visual_comparison_group_1.png
2025-08-01 10:26:08,393 - [Evaluator] - [INFO] - Visual comparison group 1 saved to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/visual_comparison_group_1.png
2025-08-01 10:26:08,393 - [Evaluator] - [INFO] - Creating visual comparison group 2 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/visual_comparison_group_2.png
2025-08-01 10:26:09,123 - [Evaluator] - [INFO] - Visual comparison group 2 saved to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/visual_comparison_group_2.png
2025-08-01 10:26:09,124 - [Evaluator] - [INFO] - Creating visual comparison group 3 to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/visual_comparison_group_3.png
2025-08-01 10:26:09,923 - [Evaluator] - [INFO] - Visual comparison group 3 saved to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/visual_comparison_group_3.png
2025-08-01 10:26:09,923 - [Evaluator] - [INFO] - Creating overview comparison image with coordinates...
2025-08-01 10:26:10,169 - [Evaluator] - [INFO] - Overview comparison image saved to /home/<USER>/rag/Diffusion-quant-rag/diffusion-quant/results/visual_comparison_overview.png