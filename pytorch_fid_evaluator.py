#!/usr/bin/env python3
"""
基于PyTorch的FID评估器，替代TensorFlow版本
使用torchvision的Inception V3模型计算FID分数
"""

import argparse
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models, transforms
from scipy import linalg
from PIL import Image
import os
from tqdm import tqdm
import logging

logger = logging.getLogger(__name__)

class InceptionV3FeatureExtractor(nn.Module):
    """基于PyTorch的Inception V3特征提取器"""
    
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 加载预训练的Inception V3模型
        self.inception = models.inception_v3(pretrained=True, transform_input=False)
        self.inception.eval()
        self.inception.to(device)
        
        # 移除最后的分类层，保留特征提取部分
        self.inception.fc = nn.Identity()
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((299, 299)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
    
    def preprocess_images(self, images):
        """预处理图像数据"""
        if isinstance(images, np.ndarray):
            # 如果是numpy数组，转换为PIL图像
            if images.dtype == np.uint8:
                images = images.astype(np.float32) / 255.0
            
            # 确保图像在[0,1]范围内
            images = np.clip(images, 0, 1)
            
            # 转换为PIL图像列表
            pil_images = []
            for img in images:
                if img.shape[-1] == 3:  # RGB
                    pil_img = Image.fromarray((img * 255).astype(np.uint8))
                else:
                    # 如果是单通道，转换为RGB
                    img_rgb = np.stack([img.squeeze()] * 3, axis=-1)
                    pil_img = Image.fromarray((img_rgb * 255).astype(np.uint8))
                pil_images.append(pil_img)
            
            # 应用预处理
            processed = torch.stack([self.transform(img) for img in pil_images])
            return processed.to(self.device)
        
        elif isinstance(images, torch.Tensor):
            # 如果已经是tensor，确保在正确的设备上
            if images.dim() == 3:
                images = images.unsqueeze(0)
            return images.to(self.device)
        
        else:
            raise ValueError(f"Unsupported image type: {type(images)}")
    
    def extract_features(self, images, batch_size=32):
        """提取图像特征"""
        if len(images) == 0:
            return np.array([])
        
        features = []
        
        with torch.no_grad():
            for i in tqdm(range(0, len(images), batch_size), desc="Extracting features"):
                batch = images[i:i+batch_size]
                batch_processed = self.preprocess_images(batch)
                
                # 提取特征
                batch_features = self.inception(batch_processed)
                
                # 如果返回的是InceptionOutputs，取logits
                if hasattr(batch_features, 'logits'):
                    batch_features = batch_features.logits
                
                # 全局平均池化
                if batch_features.dim() > 2:
                    batch_features = F.adaptive_avg_pool2d(batch_features, (1, 1))
                    batch_features = batch_features.view(batch_features.size(0), -1)
                
                features.append(batch_features.cpu().numpy())
        
        return np.concatenate(features, axis=0)

def calculate_fid(features1, features2):
    """计算两组特征之间的FID分数"""
    # 计算均值和协方差
    mu1, sigma1 = np.mean(features1, axis=0), np.cov(features1, rowvar=False)
    mu2, sigma2 = np.mean(features2, axis=0), np.cov(features2, rowvar=False)
    
    # 计算FID
    diff = mu1 - mu2
    
    # 计算协方差矩阵的平方根
    covmean, _ = linalg.sqrtm(sigma1.dot(sigma2), disp=False)
    
    # 处理数值不稳定性
    if not np.isfinite(covmean).all():
        msg = ('fid calculation produces singular product; '
               'adding %s to diagonal of cov estimates') % 1e-6
        logger.warning(msg)
        offset = np.eye(sigma1.shape[0]) * 1e-6
        covmean = linalg.sqrtm((sigma1 + offset).dot(sigma2 + offset))
    
    # 如果仍然有复数，取实部
    if np.iscomplexobj(covmean):
        if not np.allclose(np.diagonal(covmean).imag, 0, atol=1e-3):
            m = np.max(np.abs(covmean.imag))
            raise ValueError('Imaginary component {}'.format(m))
        covmean = covmean.real
    
    tr_covmean = np.trace(covmean)
    
    return (diff.dot(diff) + np.trace(sigma1) + 
            np.trace(sigma2) - 2 * tr_covmean)

def load_npz_images(npz_path):
    """从NPZ文件加载图像"""
    data = np.load(npz_path)
    
    # 尝试不同的键名
    possible_keys = ['arr_0', 'images', 'samples', 'data']
    images = None
    
    for key in possible_keys:
        if key in data:
            images = data[key]
            break
    
    if images is None:
        # 如果没有找到标准键，使用第一个数组
        keys = list(data.keys())
        if keys:
            images = data[keys[0]]
            logger.warning(f"Using key '{keys[0]}' from NPZ file")
        else:
            raise ValueError(f"No valid data found in {npz_path}")
    
    logger.info(f"Loaded {len(images)} images from {npz_path}, shape: {images.shape}")
    return images

def calculate_inception_score(features, splits=10):
    """计算Inception Score"""
    # 将特征转换为概率分布
    probs = F.softmax(torch.from_numpy(features), dim=1).numpy()
    
    # 分割数据
    split_scores = []
    N = len(probs)
    
    for i in range(splits):
        part = probs[i * N // splits: (i + 1) * N // splits]
        
        # 计算KL散度
        py = np.mean(part, axis=0)
        scores = []
        for j in range(part.shape[0]):
            pyx = part[j, :]
            scores.append(np.sum(pyx * np.log(pyx / py + 1e-8)))
        
        split_scores.append(np.exp(np.mean(scores)))
    
    return np.mean(split_scores), np.std(split_scores)

def main():
    parser = argparse.ArgumentParser(description='PyTorch FID Evaluator')
    parser.add_argument('ref_batch', help='Path to reference batch npz file')
    parser.add_argument('sample_batch', help='Path to sample batch npz file')
    parser.add_argument('--device', default='cuda', help='Device to use (cuda/cpu)')
    parser.add_argument('--batch-size', type=int, default=32, help='Batch size for feature extraction')
    parser.add_argument('--inception-score', action='store_true', help='Also calculate Inception Score')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 检查设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        logger.warning("CUDA not available, using CPU")
        args.device = 'cpu'
    
    # 创建特征提取器
    extractor = InceptionV3FeatureExtractor(device=args.device)
    
    # 加载图像
    logger.info("Loading reference images...")
    ref_images = load_npz_images(args.ref_batch)
    
    logger.info("Loading sample images...")
    sample_images = load_npz_images(args.sample_batch)
    
    # 提取特征
    logger.info("Extracting reference features...")
    ref_features = extractor.extract_features(ref_images, batch_size=args.batch_size)
    
    logger.info("Extracting sample features...")
    sample_features = extractor.extract_features(sample_images, batch_size=args.batch_size)
    
    # 计算FID
    logger.info("Calculating FID...")
    fid_score = calculate_fid(ref_features, sample_features)
    
    print(f"FID Score: {fid_score:.4f}")
    
    # 可选：计算Inception Score
    if args.inception_score:
        logger.info("Calculating Inception Score...")
        is_mean, is_std = calculate_inception_score(sample_features)
        print(f"Inception Score: {is_mean:.4f} ± {is_std:.4f}")

if __name__ == '__main__':
    main()
